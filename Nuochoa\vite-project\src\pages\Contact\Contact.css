/* ===== CONTACT PAGE STYLES ===== */
.contact-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  padding: 40px 0;
}

.contact-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 20px;
}

/* ===== HEADER SECTION ===== */
.contact-header {
  text-align: center;
  margin-bottom: 60px;
}

.contact-header h1 {
  font-size: 36px;
  font-weight: 700;
  color: #2c3e50;
  margin-bottom: 16px;
  background: linear-gradient(135deg, #3498db, #2980b9);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.contact-header p {
  font-size: 18px;
  color: #6c757d;
  max-width: 600px;
  margin: 0 auto;
  line-height: 1.6;
}

/* ===== MAIN CONTENT ===== */
.contact-content {
  display: grid;
  grid-template-columns: 1fr 1.5fr;
  gap: 60px;
  margin-bottom: 80px;
}

/* ===== CONTACT INFO ===== */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.info-card {
  background: white;
  padding: 30px;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  border: 1px solid rgba(52, 152, 219, 0.1);
}

.info-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
  border-color: rgba(52, 152, 219, 0.3);
}

.info-icon {
  font-size: 32px;
  margin-bottom: 16px;
}

.info-card h3 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 12px;
}

.info-card p {
  font-size: 16px;
  color: #495057;
  margin-bottom: 8px;
  font-weight: 500;
}

.info-note {
  font-size: 14px;
  color: #6c757d;
  font-style: italic;
}

.social-links {
  display: flex;
  gap: 12px;
  margin-top: 8px;
}

.social-link {
  padding: 8px 16px;
  border-radius: 20px;
  text-decoration: none;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.social-link.facebook {
  background: #3b5998;
  color: white;
}

.social-link.instagram {
  background: linear-gradient(45deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
  color: white;
}

.social-link.zalo {
  background: #0068ff;
  color: white;
}

.social-link:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* ===== SOCIAL CONTACT SECTION ===== */
.contact-form-section {
  background: white;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
  border: 1px solid rgba(52, 152, 219, 0.1);
}

.form-header {
  text-align: center;
  margin-bottom: 32px;
}

.form-header h2 {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.form-header p {
  font-size: 16px;
  color: #6c757d;
}

.social-contact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 25px;
  margin-top: 30px;
}

.social-contact-card {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 30px 25px;
  text-align: center;
  transition: all 0.3s ease;
  border: 2px solid transparent;
}

.social-contact-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
  border-color: #e9ecef;
}

.social-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  margin: 0 auto 20px;
  transition: all 0.3s ease;
}

.facebook-icon {
  background: linear-gradient(135deg, #3b5998, #2d4373);
  color: white;
}

.instagram-icon {
  background: linear-gradient(135deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
  color: white;
}

.zalo-icon {
  background: linear-gradient(135deg, #0068ff, #0052cc);
  color: white;
}

.social-contact-card h3 {
  font-size: 20px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 10px;
}

.social-contact-card p {
  font-size: 14px;
  color: #6c757d;
  margin-bottom: 20px;
  line-height: 1.5;
}

.contact-btn {
  display: inline-block;
  padding: 12px 24px;
  border-radius: 25px;
  text-decoration: none;
  font-weight: 600;
  font-size: 14px;
  transition: all 0.3s ease;
  color: white;
  text-align: center;
  min-width: 160px;
}

.facebook-btn {
  background: linear-gradient(135deg, #3b5998, #2d4373);
}

.facebook-btn:hover {
  background: linear-gradient(135deg, #2d4373, #1e2a4a);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(59, 89, 152, 0.3);
}

.instagram-btn {
  background: linear-gradient(135deg, #f09433, #e6683c, #dc2743, #cc2366, #bc1888);
}

.instagram-btn:hover {
  background: linear-gradient(135deg, #e6683c, #dc2743, #cc2366, #bc1888, #8a0d6b);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(240, 148, 51, 0.3);
}

.zalo-btn {
  background: linear-gradient(135deg, #0068ff, #0052cc);
}

.zalo-btn:hover {
  background: linear-gradient(135deg, #0052cc, #003d99);
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 104, 255, 0.3);
}

/* ===== FAQ SECTION ===== */
.faq-section {
  background: white;
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.faq-section h2 {
  font-size: 28px;
  font-weight: 600;
  color: #2c3e50;
  text-align: center;
  margin-bottom: 40px;
}

.faq-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.faq-item {
  padding: 24px;
  background: #f8f9fa;
  border-radius: 12px;
  border-left: 4px solid #3498db;
  transition: all 0.3s ease;
}

.faq-item:hover {
  background: #e9ecef;
  transform: translateY(-2px);
}

.faq-item h4 {
  font-size: 16px;
  font-weight: 600;
  color: #2c3e50;
  margin-bottom: 8px;
}

.faq-item p {
  font-size: 14px;
  color: #6c757d;
  line-height: 1.5;
  margin: 0;
}

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 1024px) {
  .contact-content {
    grid-template-columns: 1fr;
    gap: 40px;
  }
  
  .contact-form-section {
    padding: 30px;
  }
}

@media (max-width: 768px) {
  .contact-container {
    padding: 0 15px;
  }

  .contact-header h1 {
    font-size: 28px;
  }

  .contact-header p {
    font-size: 16px;
  }

  .social-contact-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }

  .contact-form-section {
    padding: 20px;
  }

  .info-card {
    padding: 20px;
  }

  .faq-section {
    padding: 30px 20px;
  }

  .social-links {
    flex-wrap: wrap;
  }

  .social-contact-card {
    padding: 25px 20px;
  }

  .social-icon {
    width: 50px;
    height: 50px;
    font-size: 20px;
  }

  .contact-btn {
    min-width: 140px;
    padding: 10px 20px;
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .contact-page {
    padding: 20px 0;
  }
  
  .contact-header h1 {
    font-size: 24px;
  }
  
  .form-header h2 {
    font-size: 24px;
  }
  
  .faq-section h2 {
    font-size: 24px;
  }
  
  .submit-btn {
    width: 100%;
    justify-content: center;
  }
}
